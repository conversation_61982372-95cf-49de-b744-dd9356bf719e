import { api } from '@api'
import { isValidEmail } from '@collections/validations'
import { auth, payload } from '@constants'
import { SwiftError } from '@error'
import { matchPayloadErrorMessage } from '@utils'

const { noEmail, invalidEmail, noPassword, authFailed, badCredentials, userLocked } =
  auth.errors

export const authenticate = async (credentials) => {
  const { email, password } = credentials

  if (!email) {
    throw new SwiftError(noEmail)
  }

  if (!isValidEmail(email)) {
    throw new SwiftError(invalidEmail)
  }

  if (!password) {
    throw new SwiftError(noPassword)
  }

  const body = { email, password }
  const { data, error } = await api.login({ body })

  if (error) {
    const credentialsError = matchPayloadErrorMessage(error, payload.credentialsError)

    if (credentialsError) {
      throw new SwiftError(badCredentials)
    }

    const lockedError = matchPayloadErrorMessage(error, payload.userLocked)

    if (lockedError) {
      throw new SwiftError(userLocked)
    }

    console.error('Error authenticating user', error)
    throw new SwiftError(authFailed)
  }

  return data.user
}
