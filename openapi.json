{"openapi": "3.0.0", "info": {"title": "Swift Resume API", "version": "1.0.0", "description": "Auto-generated OpenAPI document for the Swift Resume API."}, "paths": {"/api/users": {"get": {"summary": "List all users", "operationId": "findUsers", "tags": ["users"], "responses": {"200": {"description": "A list of users", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new user", "operationId": "createUser", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created user", "content": {"application/json": {}}}}}, "patch": {"summary": "Update users", "operationId": "updateUsers", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated users", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete users", "operationId": "deleteUsers", "tags": ["users"], "responses": {"204": {"description": "users deleted successfully"}}}}, "/api/users/{id}": {"get": {"summary": "Retrieve a user by ID", "operationId": "findUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single user", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a user by ID", "operationId": "updateUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated user", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a user by ID", "operationId": "deleteUserById", "tags": ["users"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "user deleted"}}}}, "/api/users/count": {"get": {"summary": "Count of users", "operationId": "countUsers", "tags": ["users"], "responses": {"200": {"description": "Count of users", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/users/login": {"post": {"summary": "<PERSON><PERSON>", "operationId": "login", "tags": ["auth"], "responses": {"200": {"description": "<PERSON><PERSON>", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/logout": {"post": {"summary": "Logout", "operationId": "logout", "tags": ["auth"], "responses": {"200": {"description": "Logout", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/unlock": {"post": {"summary": "Unlock", "operationId": "unlock", "tags": ["auth"], "responses": {"200": {"description": "Unlock", "content": {"application/json": {"schema": {"type": "object"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": "string"}}}}}}}, "/api/users/refresh-token": {"post": {"summary": "Refresh token", "operationId": "refreshToken", "tags": ["auth"], "responses": {"200": {"description": "Refresh token", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/me": {"get": {"summary": "Current user", "operationId": "currentUser", "tags": ["auth"], "responses": {"200": {"description": "Current user", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/users/forgot-password": {"post": {"summary": "Forgot password", "operationId": "forgotPassword", "tags": ["auth"], "responses": {"200": {"description": "Forgot password", "content": {"application/json": {"schema": {"type": "object"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"email": "string"}}}}}}}, "/api/users/reset-password": {"post": {"summary": "Reset password", "operationId": "resetPassword", "tags": ["auth"], "responses": {"200": {"description": "Reset password", "content": {"application/json": {"schema": {"type": "object"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"token": "string", "password": "string"}}}}}}}, "/api/users/verify/{token}": {"post": {"summary": "Verify token", "operationId": "verifyToken", "tags": ["auth"], "responses": {"200": {"description": "Verify token", "content": {"application/json": {"schema": {"type": "object"}}}}}}}, "/api/media": {"get": {"summary": "List all media", "operationId": "findMedia", "tags": ["media"], "responses": {"200": {"description": "A list of media", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new media", "operationId": "createMedia", "tags": ["media"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created media", "content": {"application/json": {}}}}}, "patch": {"summary": "Update media", "operationId": "updateMedia", "tags": ["media"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated media", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete media", "operationId": "deleteMedia", "tags": ["media"], "responses": {"204": {"description": "media deleted successfully"}}}}, "/api/media/{id}": {"get": {"summary": "Retrieve a media by ID", "operationId": "findMediaById", "tags": ["media"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single media", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a media by ID", "operationId": "updateMediaById", "tags": ["media"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated media", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a media by ID", "operationId": "deleteMediaById", "tags": ["media"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "media deleted"}}}}, "/api/media/count": {"get": {"summary": "Count of media", "operationId": "countMedia", "tags": ["media"], "responses": {"200": {"description": "Count of media", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/templates": {"get": {"summary": "List all templates", "operationId": "findTemplates", "tags": ["templates"], "responses": {"200": {"description": "A list of templates", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new template", "operationId": "createTemplate", "tags": ["templates"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created template", "content": {"application/json": {}}}}}, "patch": {"summary": "Update templates", "operationId": "updateTemplates", "tags": ["templates"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated templates", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete templates", "operationId": "deleteTemplates", "tags": ["templates"], "responses": {"204": {"description": "templates deleted successfully"}}}}, "/api/templates/{id}": {"get": {"summary": "Retrieve a template by ID", "operationId": "findTemplateById", "tags": ["templates"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single template", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a template by ID", "operationId": "updateTemplateById", "tags": ["templates"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated template", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a template by ID", "operationId": "deleteTemplateById", "tags": ["templates"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "template deleted"}}}}, "/api/templates/count": {"get": {"summary": "Count of templates", "operationId": "countTemplates", "tags": ["templates"], "responses": {"200": {"description": "Count of templates", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/resumes": {"get": {"summary": "List all resumes", "operationId": "findResumes", "tags": ["resumes"], "responses": {"200": {"description": "A list of resumes", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new resume", "operationId": "createResume", "tags": ["resumes"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created resume", "content": {"application/json": {}}}}}, "patch": {"summary": "Update resumes", "operationId": "updateResumes", "tags": ["resumes"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated resumes", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete resumes", "operationId": "deleteResumes", "tags": ["resumes"], "responses": {"204": {"description": "resumes deleted successfully"}}}}, "/api/resumes/{id}": {"get": {"summary": "Retrieve a resume by ID", "operationId": "findResumeById", "tags": ["resumes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single resume", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a resume by ID", "operationId": "updateResumeById", "tags": ["resumes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated resume", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a resume by ID", "operationId": "deleteResumeById", "tags": ["resumes"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "resume deleted"}}}}, "/api/resumes/count": {"get": {"summary": "Count of resumes", "operationId": "countResumes", "tags": ["resumes"], "responses": {"200": {"description": "Count of resumes", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/resume-examples": {"get": {"summary": "List all resume examples", "operationId": "findResumeExamples", "tags": ["resume-examples"], "responses": {"200": {"description": "A list of resume examples", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new resume example", "operationId": "createResumeExample", "tags": ["resume-examples"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created resume example", "content": {"application/json": {}}}}}, "patch": {"summary": "Update resume examples", "operationId": "updateResumeExamples", "tags": ["resume-examples"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated resume examples", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete resume examples", "operationId": "deleteResumeExamples", "tags": ["resume-examples"], "responses": {"204": {"description": "resume examples deleted successfully"}}}}, "/api/resume-examples/{id}": {"get": {"summary": "Retrieve a resume example by ID", "operationId": "findResumeExampleById", "tags": ["resume-examples"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single resume example", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a resume example by ID", "operationId": "updateResumeExampleById", "tags": ["resume-examples"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated resume example", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a resume example by ID", "operationId": "deleteResumeExampleById", "tags": ["resume-examples"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "resume example deleted"}}}}, "/api/resume-examples/count": {"get": {"summary": "Count of resume examples", "operationId": "countResumeExamples", "tags": ["resume-examples"], "responses": {"200": {"description": "Count of resume examples", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/products": {"get": {"summary": "List all products", "operationId": "findProducts", "tags": ["products"], "responses": {"200": {"description": "A list of products", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new product", "operationId": "createProduct", "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created product", "content": {"application/json": {}}}}}, "patch": {"summary": "Update products", "operationId": "updateProducts", "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated products", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete products", "operationId": "deleteProducts", "tags": ["products"], "responses": {"204": {"description": "products deleted successfully"}}}}, "/api/products/{id}": {"get": {"summary": "Retrieve a product by ID", "operationId": "findProductById", "tags": ["products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single product", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a product by ID", "operationId": "updateProductById", "tags": ["products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated product", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a product by ID", "operationId": "deleteProductById", "tags": ["products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "product deleted"}}}}, "/api/products/count": {"get": {"summary": "Count of products", "operationId": "countProducts", "tags": ["products"], "responses": {"200": {"description": "Count of products", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/subscriptions": {"get": {"summary": "List all subscriptions", "operationId": "findSubscriptions", "tags": ["subscriptions"], "responses": {"200": {"description": "A list of subscriptions", "content": {"application/json": {"schema": {"type": "object", "properties": {"docs": {"type": "array"}, "totalDocs": {"type": "integer"}, "limit": {"type": "integer"}, "totalPages": {"type": "integer"}, "page": {"type": "integer"}, "pagingCounter": {"type": "integer"}, "hasPrevPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}, "prevPage": {"type": "integer", "nullable": true}, "nextPage": {"type": "integer", "nullable": true}}}}}}}}, "post": {"summary": "Create a new subscription", "operationId": "createSubscription", "tags": ["subscriptions"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"201": {"description": "Created subscription", "content": {"application/json": {}}}}}, "patch": {"summary": "Update subscriptions", "operationId": "updateSubscriptions", "tags": ["subscriptions"], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated subscriptions", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete subscriptions", "operationId": "deleteSubscriptions", "tags": ["subscriptions"], "responses": {"204": {"description": "subscriptions deleted successfully"}}}}, "/api/subscriptions/{id}": {"get": {"summary": "Retrieve a subscription by ID", "operationId": "findSubscriptionById", "tags": ["subscriptions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "A single subscription", "content": {"application/json": {}}}}}, "patch": {"summary": "Update a subscription by ID", "operationId": "updateSubscriptionById", "tags": ["subscriptions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Updated subscription", "content": {"application/json": {}}}}}, "delete": {"summary": "Delete a subscription by ID", "operationId": "deleteSubscriptionById", "tags": ["subscriptions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "subscription deleted"}}}}, "/api/subscriptions/count": {"get": {"summary": "Count of subscriptions", "operationId": "countSubscriptions", "tags": ["subscriptions"], "responses": {"200": {"description": "Count of subscriptions", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalDocs": {"type": "integer"}}}}}}}}}, "/api/otp/request-otp": {"post": {"summary": "Request OTP", "operationId": "requestOtp", "tags": ["otp"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "format": "email", "description": "Email address to send OTP to"}}}}}}, "responses": {"200": {"description": "OTP sent successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "OTP sent"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "object", "description": "Error details"}}}}}}}}}, "/api/otp/verify-otp": {"post": {"summary": "Verify OTP", "operationId": "verifyOtp", "tags": ["otp"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "otp"], "properties": {"email": {"type": "string", "format": "email", "description": "Email address associated with the OTP"}, "otp": {"type": "string", "pattern": "^[0-9]{6}$", "description": "6-digit OTP code"}}}}}}, "responses": {"200": {"description": "OTP verified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "OTP verified"}}}}}}, "400": {"description": "Invalid OTP or user not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "enum": ["Invalid OTP", "User not found"]}}}}}}}}}, "/api/products/country-based-products-list": {"get": {"summary": "Get country-based products list", "operationId": "getCountryBasedProductsList", "tags": ["products"], "parameters": [{"name": "country", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Country code (e.g., US, GB, DE)"}, {"name": "currency", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Currency code (e.g., usd, eur, gbp)"}], "responses": {"200": {"description": "Country-based products list", "content": {"application/json": {"schema": {"type": "object", "properties": {"country": {"type": "string"}, "currency": {"type": "string"}, "symbol": {"type": "string"}, "products": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "priceJSON": {"type": "object"}, "trialpriceJSON": {"type": "object"}, "stripeProductID": {"type": "string"}, "trialStripeProductID": {"type": "string"}, "isPopular": {"type": "boolean"}, "trialPeriod": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}}, "total": {"type": "number"}}}}}}, "400": {"description": "Invalid country or currency", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}}}