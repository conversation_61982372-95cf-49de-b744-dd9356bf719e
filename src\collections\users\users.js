import { isAdmin, isAdmin<PERSON>r<PERSON><PERSON>, isAnyone } from '@collections/access'
import { isValidEmail, isValidPassword } from '@collections/validations'
import { ForgotPasswordEmail } from '@components'
import { collections } from '@constants'
import { socialCallbackHandler } from '@handlers'
import { render } from '@react-email/render'
import { createResumeForAnonUser, sendWelcomeEmail } from './hooks'

export const users = {
  slug: collections.users.slug,
  access: {
    create: isAnyone,
    read: isAdminOrSelf,
    update: isAdminOrSelf,
    delete: isAdminOrSelf
  },
  auth: {
    depth: 0,
    tokenExpiration: 1.21e6, // 14 days
    maxLoginAttempts: 5,
    lockTime: 600_000, // 10 mins,
    forgotPassword: {
      generateEmailHTML: async ({ token, user }) =>
        await render(ForgotPasswordEmail({ token, user }))
    }
  },
  endpoints: [
    {
      path: '/oauth/:provider/callback',
      method: 'get',
      handler: social<PERSON>allback<PERSON><PERSON><PERSON>,
      summary: 'Social login callback'
    }
  ],
  admin: {
    useAsTitle: 'email'
  },
  timestamps: true,
  fields: [
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true,
      validate: isValidEmail
    },
    {
      name: 'password',
      type: 'password',
      validate: isValidPassword
    },
    {
      name: 'socialLogins',
      type: 'array',
      label: 'Social Logins',
      access: {
        read: isAdminOrSelf,
        update: isAdmin
      },
      fields: [
        {
          name: 'provider',
          type: 'text',
          required: true
        },
        {
          name: 'sub',
          type: 'text',
          required: true
        }
      ]
    },
    {
      name: 'firstName',
      type: 'text',
      minLength: collections.shared.minLength,
      maxLength: collections.shared.maxLength
    },
    {
      name: 'lastName',
      type: 'text',
      minLength: collections.shared.minLength,
      maxLength: collections.shared.maxLength
    },
    {
      name: 'role',
      type: 'select',
      saveToJWT: true,
      hasMany: false,
      access: {
        create: isAnyone,
        read: isAnyone,
        update: isAdmin
      },
      options: [
        {
          label: collections.users.roles.member,
          value: collections.users.roles.member
        },
        {
          label: collections.users.roles.anon,
          value: collections.users.roles.anon
        }
      ]
    },
    {
      name: 'resumes',
      type: 'join',
      collection: 'resumes',
      on: 'user'
    },
    {
      name: 'avatarUrl',
      type: 'text'
    },
    {
      name: 'avatarId',
      type: 'text'
    },
    {
      name: 'subscription',
      type: 'relationship',
      relationTo: 'subscriptions',
      hasMany: false,
      admin: {
        position: 'sidebar'
      }
    },
    {
      name: 'stripeCustomerId',
      label: 'Stripe Customer ID',
      type: 'text',
      admin: {
        position: 'sidebar'
      }
    }
  ],
  hooks: {
    afterChange: [sendWelcomeEmail, createResumeForAnonUser]
  }
}
