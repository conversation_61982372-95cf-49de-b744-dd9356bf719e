'use client'

import { useTranslations } from 'next-intl'
import { cn } from '@utils'
import { textVariants } from './Text'

export const ErrorDisplay = ({ error, component }) => {
  const t = useTranslations('Errors')

  console.log('error', error)

  if (!error) {
    return null
  }

  const message = t(error.digest) || t('unexpectedError')

  return (
    <div
      className={cn(textVariants({ variant: 'xs', weight: 'medium' }), 'text-red-500')}>
      {component || <>{message}</>}
    </div>
  )
}
