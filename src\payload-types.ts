/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    admins: AdminAuthOperations;
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    admins: Admin;
    users: User;
    media: Media;
    templates: Template;
    resumes: Resume;
    'resume-examples': ResumeExample;
    products: Product;
    subscriptions: Subscription;
    pricingplans: Pricingplan;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {
    users: {
      resumes: 'resumes';
    };
  };
  collectionsSelect: {
    admins: AdminsSelect<false> | AdminsSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    templates: TemplatesSelect<false> | TemplatesSelect<true>;
    resumes: ResumesSelect<false> | ResumesSelect<true>;
    'resume-examples': ResumeExamplesSelect<false> | ResumeExamplesSelect<true>;
    products: ProductsSelect<false> | ProductsSelect<true>;
    subscriptions: SubscriptionsSelect<false> | SubscriptionsSelect<true>;
    pricingplans: PricingplansSelect<false> | PricingplansSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user:
    | (Admin & {
        collection: 'admins';
      })
    | (User & {
        collection: 'users';
      });
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface AdminAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "admins".
 */
export interface Admin {
  id: string;
  firstName?: string | null;
  lastName?: string | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  socialLogins?:
    | {
        provider: string;
        sub: string;
        id?: string | null;
      }[]
    | null;
  firstName?: string | null;
  lastName?: string | null;
  role?: ('member' | 'anon') | null;
  resumes?: {
    docs?: (string | Resume)[];
    hasNextPage?: boolean;
    totalDocs?: number;
  };
  avatarUrl?: string | null;
  avatarId?: string | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "resumes".
 */
export interface Resume {
  id: string;
  name?: string | null;
  user?: (string | null) | User;
  /**
   * Add a profile image
   */
  profileImage?: (string | null) | Media;
  showProfileImage?: boolean | null;
  jobTitle?: string | null;
  summary?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  firstName?: string | null;
  lastName?: string | null;
  phone?: string | null;
  email?: string | null;
  links?:
    | {
        name?: string | null;
        url?: string | null;
        id?: string | null;
      }[]
    | null;
  experience?:
    | {
        title?: string | null;
        subTitle?: string | null;
        isInternship?: boolean | null;
        startMonth?: string | null;
        startYear?: string | null;
        endMonth?: string | null;
        endYear?: string | null;
        isPresent?: boolean | null;
        description?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        isVisible?: boolean | null;
        id?: string | null;
      }[]
    | null;
  skills?:
    | {
        skill?: string | null;
        rating?: number | null;
        isVisible?: boolean | null;
        id?: string | null;
      }[]
    | null;
  education?:
    | {
        title?: string | null;
        subTitle?: string | null;
        startMonth?: string | null;
        startYear?: string | null;
        endMonth?: string | null;
        endYear?: string | null;
        isPresent?: boolean | null;
        description?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        isVisible?: boolean | null;
        id?: string | null;
      }[]
    | null;
  proficiencies?:
    | {
        title?: string | null;
        subTitle?: string | null;
        startMonth?: string | null;
        startYear?: string | null;
        showDate?: boolean | null;
        description?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        isVisible?: boolean | null;
        id?: string | null;
      }[]
    | null;
  customSections?:
    | {
        title?: string | null;
        subTitle?: string | null;
        startMonth?: string | null;
        startYear?: string | null;
        showDate?: boolean | null;
        description?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        isVisible?: boolean | null;
        id?: string | null;
      }[]
    | null;
  templateId?: (string | null) | Template;
  templateName?: string | null;
  design?: {
    layout?: ('sidebarLeft' | 'sidebarRight' | 'fullWidth' | 'fullSplit') | null;
    sidebarStyle?: ('normal' | 'inset') | null;
    spacing?: ('tight' | 'normal' | 'loose') | null;
    accent?: ('none' | 'ribbon' | 'blur' | 'header') | null;
    font?: 'Inter' | null;
    headingStyle?: 'default' | null;
    headingUnderlineStyle?: ('default' | 'underline' | 'dotted' | 'squiggle') | null;
    palette?:
      | ('cloud' | 'zinc' | 'electric' | 'pomegranate' | 'emerald' | 'royal' | 'ember' | 'blush' | 'slate' | 'stone')
      | null;
    avatarStyle?: ('square' | 'rounded' | 'circle') | null;
    accentOptions?: string | null;
    backgroundPureWhite?: boolean | null;
    pageNumbers?: boolean | null;
    sections?:
      | {
          sectionKey: 'personalDetails' | 'experience' | 'skills' | 'education' | 'proficiencies' | 'customSections';
          showInSidebar?: boolean | null;
          isVisible?: boolean | null;
          sectionName?: string | null;
          id?: string | null;
        }[]
      | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * Media files uploaded to the system
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: string;
  alt?: string | null;
  uploadedBy?:
    | ({
        relationTo: 'admins';
        value: string | Admin;
      } | null)
    | ({
        relationTo: 'users';
        value: string | User;
      } | null);
  prefix?: string | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "templates".
 */
export interface Template {
  id: string;
  templateName: string;
  user?: (string | null) | User;
  layout?: ('sidebarLeft' | 'sidebarRight' | 'fullWidth' | 'fullSplit') | null;
  sidebarStyle?: ('normal' | 'inset') | null;
  spacing?: ('tight' | 'normal' | 'loose') | null;
  accent?: ('none' | 'ribbon' | 'blur' | 'header') | null;
  font?: 'Inter' | null;
  headingStyle?: 'default' | null;
  headingUnderlineStyle?: ('default' | 'underline' | 'dotted' | 'squiggle') | null;
  palette?:
    | ('cloud' | 'zinc' | 'electric' | 'pomegranate' | 'emerald' | 'royal' | 'ember' | 'blush' | 'slate' | 'stone')
    | null;
  avatarStyle?: ('square' | 'rounded' | 'circle') | null;
  accentOptions?: string | null;
  backgroundPureWhite?: boolean | null;
  pageNumbers?: boolean | null;
  sections?:
    | {
        sectionKey: 'personalDetails' | 'experience' | 'skills' | 'education' | 'proficiencies' | 'customSections';
        showInSidebar?: boolean | null;
        isVisible?: boolean | null;
        sectionName?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "resume-examples".
 */
export interface ResumeExample {
  id: string;
  name?: string | null;
  user?: (string | null) | User;
  /**
   * Add a profile image
   */
  profileImage?: (string | null) | Media;
  showProfileImage?: boolean | null;
  jobTitle?: string | null;
  summary?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  firstName?: string | null;
  lastName?: string | null;
  phone?: string | null;
  email?: string | null;
  links?:
    | {
        name?: string | null;
        url?: string | null;
        id?: string | null;
      }[]
    | null;
  experience?:
    | {
        title?: string | null;
        subTitle?: string | null;
        isInternship?: boolean | null;
        startMonth?: string | null;
        startYear?: string | null;
        endMonth?: string | null;
        endYear?: string | null;
        isPresent?: boolean | null;
        description?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        isVisible?: boolean | null;
        id?: string | null;
      }[]
    | null;
  skills?:
    | {
        skill?: string | null;
        rating?: number | null;
        isVisible?: boolean | null;
        id?: string | null;
      }[]
    | null;
  education?:
    | {
        title?: string | null;
        subTitle?: string | null;
        startMonth?: string | null;
        startYear?: string | null;
        endMonth?: string | null;
        endYear?: string | null;
        isPresent?: boolean | null;
        description?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        isVisible?: boolean | null;
        id?: string | null;
      }[]
    | null;
  proficiencies?:
    | {
        title?: string | null;
        subTitle?: string | null;
        startMonth?: string | null;
        startYear?: string | null;
        showDate?: boolean | null;
        description?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        isVisible?: boolean | null;
        id?: string | null;
      }[]
    | null;
  customSections?:
    | {
        title?: string | null;
        subTitle?: string | null;
        startMonth?: string | null;
        startYear?: string | null;
        showDate?: boolean | null;
        description?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        isVisible?: boolean | null;
        id?: string | null;
      }[]
    | null;
  templateId?: (string | null) | Template;
  templateName?: string | null;
  design?: {
    layout?: ('sidebarLeft' | 'sidebarRight' | 'fullWidth' | 'fullSplit') | null;
    sidebarStyle?: ('normal' | 'inset') | null;
    spacing?: ('tight' | 'normal' | 'loose') | null;
    accent?: ('none' | 'ribbon' | 'blur' | 'header') | null;
    font?: 'Inter' | null;
    headingStyle?: 'default' | null;
    headingUnderlineStyle?: ('default' | 'underline' | 'dotted' | 'squiggle') | null;
    palette?:
      | ('cloud' | 'zinc' | 'electric' | 'pomegranate' | 'emerald' | 'royal' | 'ember' | 'blush' | 'slate' | 'stone')
      | null;
    avatarStyle?: ('square' | 'rounded' | 'circle') | null;
    accentOptions?: string | null;
    backgroundPureWhite?: boolean | null;
    pageNumbers?: boolean | null;
    sections?:
      | {
          sectionKey: 'personalDetails' | 'experience' | 'skills' | 'education' | 'proficiencies' | 'customSections';
          showInSidebar?: boolean | null;
          isVisible?: boolean | null;
          sectionName?: string | null;
          id?: string | null;
        }[]
      | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products".
 */
export interface Product {
  id: string;
  name: string;
  description?: string | null;
  amount?: number | null;
  stripeID?: string | null;
  skipSync?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "subscriptions".
 */
export interface Subscription {
  id: string;
  subscriptionId: string;
  user?: (string | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pricingplans".
 */
export interface Pricingplan {
  id: string;
  type: string;
  unit_amount?: number | null;
  product?: string | null;
  currency?: string | null;
  recurring?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  stripeID?: string | null;
  skipSync?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'admins';
        value: string | Admin;
      } | null)
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'media';
        value: string | Media;
      } | null)
    | ({
        relationTo: 'templates';
        value: string | Template;
      } | null)
    | ({
        relationTo: 'resumes';
        value: string | Resume;
      } | null)
    | ({
        relationTo: 'resume-examples';
        value: string | ResumeExample;
      } | null)
    | ({
        relationTo: 'products';
        value: string | Product;
      } | null)
    | ({
        relationTo: 'subscriptions';
        value: string | Subscription;
      } | null)
    | ({
        relationTo: 'pricingplans';
        value: string | Pricingplan;
      } | null);
  globalSlug?: string | null;
  user:
    | {
        relationTo: 'admins';
        value: string | Admin;
      }
    | {
        relationTo: 'users';
        value: string | User;
      };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user:
    | {
        relationTo: 'admins';
        value: string | Admin;
      }
    | {
        relationTo: 'users';
        value: string | User;
      };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "admins_select".
 */
export interface AdminsSelect<T extends boolean = true> {
  password?: T;
  firstName?: T;
  lastName?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  password?: T;
  socialLogins?:
    | T
    | {
        provider?: T;
        sub?: T;
        id?: T;
      };
  firstName?: T;
  lastName?: T;
  role?: T;
  resumes?: T;
  avatarUrl?: T;
  avatarId?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  uploadedBy?: T;
  prefix?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "templates_select".
 */
export interface TemplatesSelect<T extends boolean = true> {
  templateName?: T;
  user?: T;
  layout?: T;
  sidebarStyle?: T;
  spacing?: T;
  accent?: T;
  font?: T;
  headingStyle?: T;
  headingUnderlineStyle?: T;
  palette?: T;
  avatarStyle?: T;
  accentOptions?: T;
  backgroundPureWhite?: T;
  pageNumbers?: T;
  sections?:
    | T
    | {
        sectionKey?: T;
        showInSidebar?: T;
        isVisible?: T;
        sectionName?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "resumes_select".
 */
export interface ResumesSelect<T extends boolean = true> {
  name?: T;
  user?: T;
  profileImage?: T;
  showProfileImage?: T;
  jobTitle?: T;
  summary?: T;
  firstName?: T;
  lastName?: T;
  phone?: T;
  email?: T;
  links?:
    | T
    | {
        name?: T;
        url?: T;
        id?: T;
      };
  experience?:
    | T
    | {
        title?: T;
        subTitle?: T;
        isInternship?: T;
        startMonth?: T;
        startYear?: T;
        endMonth?: T;
        endYear?: T;
        isPresent?: T;
        description?: T;
        isVisible?: T;
        id?: T;
      };
  skills?:
    | T
    | {
        skill?: T;
        rating?: T;
        isVisible?: T;
        id?: T;
      };
  education?:
    | T
    | {
        title?: T;
        subTitle?: T;
        startMonth?: T;
        startYear?: T;
        endMonth?: T;
        endYear?: T;
        isPresent?: T;
        description?: T;
        isVisible?: T;
        id?: T;
      };
  proficiencies?:
    | T
    | {
        title?: T;
        subTitle?: T;
        startMonth?: T;
        startYear?: T;
        showDate?: T;
        description?: T;
        isVisible?: T;
        id?: T;
      };
  customSections?:
    | T
    | {
        title?: T;
        subTitle?: T;
        startMonth?: T;
        startYear?: T;
        showDate?: T;
        description?: T;
        isVisible?: T;
        id?: T;
      };
  templateId?: T;
  templateName?: T;
  design?:
    | T
    | {
        layout?: T;
        sidebarStyle?: T;
        spacing?: T;
        accent?: T;
        font?: T;
        headingStyle?: T;
        headingUnderlineStyle?: T;
        palette?: T;
        avatarStyle?: T;
        accentOptions?: T;
        backgroundPureWhite?: T;
        pageNumbers?: T;
        sections?:
          | T
          | {
              sectionKey?: T;
              showInSidebar?: T;
              isVisible?: T;
              sectionName?: T;
              id?: T;
            };
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "resume-examples_select".
 */
export interface ResumeExamplesSelect<T extends boolean = true> {
  name?: T;
  user?: T;
  profileImage?: T;
  showProfileImage?: T;
  jobTitle?: T;
  summary?: T;
  firstName?: T;
  lastName?: T;
  phone?: T;
  email?: T;
  links?:
    | T
    | {
        name?: T;
        url?: T;
        id?: T;
      };
  experience?:
    | T
    | {
        title?: T;
        subTitle?: T;
        isInternship?: T;
        startMonth?: T;
        startYear?: T;
        endMonth?: T;
        endYear?: T;
        isPresent?: T;
        description?: T;
        isVisible?: T;
        id?: T;
      };
  skills?:
    | T
    | {
        skill?: T;
        rating?: T;
        isVisible?: T;
        id?: T;
      };
  education?:
    | T
    | {
        title?: T;
        subTitle?: T;
        startMonth?: T;
        startYear?: T;
        endMonth?: T;
        endYear?: T;
        isPresent?: T;
        description?: T;
        isVisible?: T;
        id?: T;
      };
  proficiencies?:
    | T
    | {
        title?: T;
        subTitle?: T;
        startMonth?: T;
        startYear?: T;
        showDate?: T;
        description?: T;
        isVisible?: T;
        id?: T;
      };
  customSections?:
    | T
    | {
        title?: T;
        subTitle?: T;
        startMonth?: T;
        startYear?: T;
        showDate?: T;
        description?: T;
        isVisible?: T;
        id?: T;
      };
  templateId?: T;
  templateName?: T;
  design?:
    | T
    | {
        layout?: T;
        sidebarStyle?: T;
        spacing?: T;
        accent?: T;
        font?: T;
        headingStyle?: T;
        headingUnderlineStyle?: T;
        palette?: T;
        avatarStyle?: T;
        accentOptions?: T;
        backgroundPureWhite?: T;
        pageNumbers?: T;
        sections?:
          | T
          | {
              sectionKey?: T;
              showInSidebar?: T;
              isVisible?: T;
              sectionName?: T;
              id?: T;
            };
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "products_select".
 */
export interface ProductsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  amount?: T;
  stripeID?: T;
  skipSync?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "subscriptions_select".
 */
export interface SubscriptionsSelect<T extends boolean = true> {
  subscriptionId?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pricingplans_select".
 */
export interface PricingplansSelect<T extends boolean = true> {
  type?: T;
  unit_amount?: T;
  product?: T;
  currency?: T;
  recurring?: T;
  stripeID?: T;
  skipSync?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}