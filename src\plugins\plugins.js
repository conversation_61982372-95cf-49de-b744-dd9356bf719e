import { collections } from '@constants'
import { stripePlugin } from '@payloadcms/plugin-stripe'
import { s3Storage } from '@payloadcms/storage-s3'
import {
  customerCreated,
  customerDeleted,
  priceUpdated,
  productUpdated,
  subscriptionCreated,
  subscriptionDeleted,
  subscriptionUpdated
} from '@stripe/webhooks'

// import { webhooks } from './hooks'

export const plugins = [
  s3Storage({
    collections: {
      media: {
        disableLocalStorage: true,
        disablePayloadAccessControl: true,
        prefix: collections.media.uploadDir
      }
    },
    config: {
      forcePathStyle: false,
      credentials: {
        accessKeyId: process.env.DO_S3_ACCESS_KEY,
        secretAccessKey: process.env.DO_S3_SECRET_KEY
      },
      region: process.env.DO_S3_REGION,
      endpoint: process.env.DO_S3_ENDPOINT
    },
    bucket: process.env.DO_S3_BUCKET,
    acl: 'public-read'
  }),
  stripePlugin({
    stripeSecretKey: process.env.STRIPE_SECRET_KEY,
    stripeWebhooksEndpointSecret: process.env.STRIPE_WEBHOOK_SECRET,
    rest: true,
    webhooks: {
      'product.created': productUpdated,
      'product.updated': productUpdated,
      'price.updated': priceUpdated,
      'customer.created': customerCreated,
      'customer.deleted': customerDeleted,
      'customer.subscription.created': subscriptionCreated,
      'customer.subscription.updated': subscriptionUpdated,
      'customer.subscription.deleted': subscriptionDeleted
    },
    logs: true
  })
]
